body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
    background: url("@/assets/image/bg.png") no-repeat center center;
    background-size: 100% 100%;
    background-position: center;
    position: relative;
}

/* Add shooting stars animation */
body::before,
body::after {
    content: '';
    position: fixed; /* Changed from absolute to fixed */
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1),
                0 0 0 8px rgba(255, 255, 255, 0.1),
                0 0 20px rgba(255, 255, 255, 1);
    animation: shooting 3s linear infinite;
}

body::before {
    top: 50%;
    left: 50%;
    animation-delay: 0.5s;
}

body::after {
    top: 80%;
    left: 20%;
    animation-delay: 1.5s;
}

@keyframes shooting {
    0% {
        transform: rotate(315deg) translateX(0);
        opacity: 1;
    }
    70% {
        opacity: 1;
    }
    100% {
        transform: rotate(315deg) translateX(1000px);
        opacity: 0;
    }
}

/* Add twinkling stars */
body::before {
    content: '';
    position: fixed; /* Changed from absolute to fixed */
    width: 100%;
    height: 100%;
    background: radial-gradient(2px 2px at 20px 30px, #fff, rgba(0,0,0,0)),
                radial-gradient(2px 2px at 40px 70px, #fff, rgba(0,0,0,0)),
                radial-gradient(2px 2px at 50px 160px, #fff, rgba(0,0,0,0)),
                radial-gradient(2px 2px at 90px 40px, #fff, rgba(0,0,0,0)),
                radial-gradient(2px 2px at 130px 80px, #fff, rgba(0,0,0,0));
    background-repeat: repeat;
    animation: twinkle 4s linear infinite;
}

@keyframes twinkle {
    0% { opacity: 0.3; }
    50% { opacity: 0.8; }
    100% { opacity: 0.3; }
}

.chart-container {
    width: 100%;
    /* background-color: rgba(14, 40, 73, 0.3); */
    /* Added 7.5% top offset to center vertically */
    text-align: left;
    display: flex;
    align-items: center;
    backdrop-filter: blur(0.625rem);
    transition: all 0.3s ease;
    box-sizing: border-box;
    overflow: hidden;
}

.chart-title {
    color: #fff;
    font-weight: bolder;
    letter-spacing: 0.2rem;
    font-family: "Arial", sans-serif;
    background: url("@/assets/image/v1-17.png") no-repeat left center;
    background-size: 55% 120%;
    background-position: left;
    text-align: left;
    height: 4rem;
    position: relative;
    display: flex;
    align-items: center;

    h1 {
        font-size: 1.2rem;
        color: #fff;
        font-weight: bolder;
        letter-spacing: 0.2rem;
        font-family: "Arial", sans-serif;
        margin: 0;
        position: absolute;
        left: 8.5rem;
        text-shadow: 0 0 10px #F4CA16;
        font-style: italic;
    }
}

.dashboard {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0;
    margin: 0;

    .header {
        height: 7rem;
        text-align: center;
        line-height: 2.5rem;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: flex-start;
        padding: 0.1rem 2rem;
        background: url("@/assets/image/2-1.png") no-repeat center center;
        background-size: 100% 100%;
        background-position: center;

        .header-left {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            flex: 1;

            .title-item {
                flex: 1;
                height: 3.5rem;
                line-height: 0.85rem;
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                background: url("@/assets/image/V4-14.png") no-repeat center center;
                background-size: 90% 240%;
                background-position: center;
                margin: 4.5rem 0rem;

                h4 {
                    font-size: 1rem;
                    color: #feffff;
                    font-weight: bolder;
                    letter-spacing: 0.2rem;
                    font-family: "Arial", sans-serif;
                }
            }
        }

        .header-center {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: center;

            h2 {
                font-size: 2rem;
                color: #feffff;
                font-weight: bolder;
                letter-spacing: 0.3rem;
                font-family: "Orbitron", "Rajdhani", "Audiowide", sans-serif;
                /* text-shadow: 0 0 10px #feffff, 0 0 20px rgba(255, 255, 255, 0.5); */
                background: linear-gradient(180deg, #ffffff 100%, #3AFEFE 80%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                animation: glow 1.5s ease-in-out infinite alternate;
            }

            @keyframes glow {
                from {
                    text-shadow: 0 0 10px #feffff, 0 0 20px #3AFEFE;
                }
                to {
                    text-shadow: 0 0 15px #feffff, 0 0 25px #3AFEFE;
                }
            }
        }

        .header-right {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;

            .title-item {
                flex: 1;
                height: 3.5rem;
                line-height: 0.85rem;
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                background: url("@/assets/image/V4-14.png") no-repeat center center;
                background-size: 90% 240%;
                background-position: center;
                margin: 4.5rem 0rem;

                h4 {
                    font-size: 1rem;
                    color: #feffff;
                    font-weight: bolder;
                    letter-spacing: 0.2rem;
                    font-family: "Arial", sans-serif;
                }
            }
        }
    }

    .middle {
        height: calc(100vh - 7.5rem);
        display: flex;
        flex-direction: row;
        margin: 0 3.5rem;
        /* background: url("@/assets/image/bg.jpg") no-repeat center center;
        background-size: 95% 95%;
        background-position: center; */

        .left {
            width: 25%;
            height: 100%;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;

            .left-containers {
                height: 100%;
                box-sizing: border-box;
                overflow: hidden;

                .chart-containers {
                    height: 95vh;
                }
            }
        }

        .center {
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .top {
                height: 50%;

                .bottom-top {
                    height: 100%;
                }
            }

            .bottom {
                height: 50%;

                .bottom-center {
                    height: 100%;
                }
            }
        }

        .right {
            width: 25%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .right-content {
                height: 100vh;

                .chart-containers {
                    height: 95vh;
                }
            }
        }
    }
}