<template>
  <div class="middle-section">
    <!-- Left Section -->
    <div class="left-section">
      <div class="chart-content" @click="toggleFullscreen('left', 0)">
        <div class="chart-title">
          <h1>各MODEL维修次数</h1>
        </div>
        <div class="chart-container">
          <RankList />
        </div>
      </div>
      <div class="chart-content" @click="toggleFullscreen('left', 1)">
        <div class="chart-title">
          <h1>平均每次接单等待时间</h1>
        </div>
        <div class="chart-container">
          <ChartLine />
        </div>
      </div>
    </div>

    <!-- Center Section -->
    <div class="center-section">
      <!-- 上半部分 -->
      <div class="scroll-content-top">
        <div class="base" style="top: 5%">
          <!-- Adjusted position -->
          <img src="@/assets/image/bottom.png" alt="底座" class="base-image" />
          <div class="cylinders">
            <div class="cylinder" style="top: 9%; left: 45%">
              <span class="cylinder-number">12</span>
              <img
                src="@/assets/image/v01.png"
                alt="圆柱体"
                class="cylinder-image"
              />
              <span class="repair-duration">待维修机台</span>
            </div>
            <div class="cylinder" style="top: 15%; left: 20%">
              <span class="cylinder-number">2520</span>
              <img
                src="@/assets/image/v01.png"
                alt="圆柱体"
                class="cylinder-image"
              />
              <span class="repair-duration">总维修次数</span>
            </div>
            <div class="cylinder" style="top: 15%; left: 70%">
              <span class="cylinder-number">133</span>
              <img
                src="@/assets/image/v01.png"
                alt="圆柱体"
                class="cylinder-image"
              />
              <span class="repair-duration">报修人数</span>
            </div>
            <div class="cylinder" style="top: 65%; left: 45%">
              <span class="cylinder-number">400</span>
              <img
                src="@/assets/image/v01.png"
                alt="圆柱体"
                class="cylinder-image"
              />
              <span class="repair-duration">维修时长</span>
            </div>
            <div class="cylinder" style="top: 45%; left: 85%">
              <span class="cylinder-number">15</span>
              <img
                src="@/assets/image/v01.png"
                alt="圆柱体"
                class="cylinder-image"
              />
              <span class="repair-duration">机修人数</span>
            </div>
            <div class="cylinder" style="top: 45%; left: 4%">
              <span class="cylinder-number">156</span>
              <img
                src="@/assets/image/v01.png"
                alt="圆柱体"
                class="cylinder-image"
              />
              <span class="repair-duration">超时接单</span>
            </div>
          </div>
          <div class="star-effect"></div>
        </div>
      </div>
      <!-- 下面部分 -->
      <div class="scroll-content-bottom">
        <div class="chart-content">
          <div class="chart-title">
            <h1>维修信息时报</h1>
          </div>
          <div class="chart-container marquee">
            <div class="table-header">
              <span>机台号</span>
              <span>接单时间</span>
              <span>Model</span>
              <span>故障原因</span>
              <span>维修人员</span>
              <span>总维修次数</span>
              <span>维修描述</span>
            </div>
            <div class="marquee-content">
              <!-- table内容 -->
              <div class="table-content" v-for="item in 20" :key="item">
                <div class="table-item">
                  <span>F0174</span>
                  <span>2024-12-12 12:00:00</span>
                  <span>FREE RUN</span>
                  <span>断F丝</span>
                  <span>范青太和</span>
                  <span>11</span>
                  <span>Tim Kim</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Section -->
    <div class="right-section">
      <!-- 上半部分 -->
      <div class="chart-content" @click="toggleFullscreen('right', 0)">
        <div class="chart-title">
          <h1>各机修维修次数和时长</h1>
        </div>
        <div class="chart-container">
          <ChartBar
            :xData="xData"
            :yData1="yData1"
            :yData2="yData2"
            :colors="['#F4CA16', '#418FDE']"
          />
        </div>
      </div>
      <!-- 下半部分 -->
      <div class="chart-content" @click="toggleFullscreen('right', 1)">
        <div class="chart-title">
          <h1>各疵点维修次数和时长</h1>
        </div>
        <div class="chart-container">
          <ChartBar
            :xData="xData1"
            :yData1="yData2"
            :yData2="yData4"
            :colors="['#6599FC', '#6953D8']"
          />
        </div>
      </div>
    </div>

    <!-- Fullscreen Modal -->
    <Transition name="modal">
      <div v-if="fullscreenSection" class="modal-overlay" @click.self="closeFullscreen">
        <div class="modal-content">
          <div class="modal-header">
            <h2>{{ getModalTitle }}</h2>
            <button class="close-btn" @click="closeFullscreen">&times;</button>
          </div>
          <div class="modal-body">
            <component 
              :is="getFullscreenComponent"
              v-bind="getComponentProps"
            />
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import ChartBar from "../components/ChartBar.vue";
import ChartLine from "../components/ChartLine.vue";
import ChartPie from "../components/ChartPie.vue";
import ChartRada from "../components/ChartRada.vue";
import RankList from "../components/RankList.vue";

defineOptions({
  name: "MiddlePage1",
});

const xData = [
  "2024-12-01",
  "2024-12-02",
  "2024-12-03",
  "2024-12-04",
  "2024-12-05",
  "2024-12-06",
  "2024-12-07",
];
const yData1 = [120, 200, 150, 80, 70, 110, 130];
const yData2 = [15, 18, 13, 10, 9, 14, 16];

const xData1 = ["漏针", "挂针", "油针油污", "其他故障", "破洞", "脏污", "断纱"];
const yData3 = [11, 22, 18, 9, 10, 13, 30];
const yData4 = [15.3, 18.2, 13.1, 10.4, 9.5, 14.6, 16.7];

const fullscreenSection = ref(null);
const fullscreenIndex = ref(null);

const toggleFullscreen = (section, index) => {
  fullscreenSection.value = section;
  fullscreenIndex.value = index;
};

const closeFullscreen = () => {
  fullscreenSection.value = null;
  fullscreenIndex.value = null;
};

const getModalTitle = computed(() => {
  if (fullscreenSection.value === 'left') {
    return fullscreenIndex.value === 0 ? '各MODEL维修次数' : '平均每次接单等待时间';
  } else if (fullscreenSection.value === 'center') {
    return fullscreenIndex.value === 0 ? '维修数据总览' : '维修信息时报';
  } else if (fullscreenSection.value === 'right') {
    return fullscreenIndex.value === 0 ? '各机修维修次数和时长' : '各疵点维修次数和时长';
  }
  return '';
});

const getFullscreenComponent = computed(() => {
  if (fullscreenSection.value === 'left') {
    return fullscreenIndex.value === 0 ? RankList : ChartLine;
  } else if (fullscreenSection.value === 'center') {
    if (fullscreenIndex.value === 0) {
      return {
        template: `
          <div class="base fullscreen-base">
            <img src="@/assets/image/bottom.png" alt="底座" class="base-image" />
            <div class="cylinders">
              <!-- 复制原有的圆柱体内容 -->
              <div class="cylinder" style="top: 9%; left: 45%">
                <span class="cylinder-number">12</span>
                <img src="@/assets/image/v01.png" alt="圆柱体" class="cylinder-image" />
                <span class="repair-duration">待维修机台</span>
              </div>
              <!-- ... 其他圆柱体 ... -->
            </div>
          </div>
        `
      };
    } else {
      return {
        template: `
          <div class="fullscreen-table">
            <!-- 复制原有的表格内容 -->
            <div class="table-header">
              <span>机台号</span>
              <span>接单时间</span>
              <span>Model</span>
              <span>故障原因</span>
              <span>维修人员</span>
              <span>总维修次数</span>
              <span>维修描述</span>
            </div>
            <div class="marquee-content">
              <!-- ... 表格内容 ... -->
            </div>
          </div>
        `
      };
    }
  } else if (fullscreenSection.value === 'right') {
    return ChartBar;
  }
  return null;
});

const getComponentProps = computed(() => {
  if (fullscreenSection.value === 'right') {
    if (fullscreenIndex.value === 0) {
      return {
        xData: xData,
        yData1: yData1,
        yData2: yData2,
        colors: ['#F4CA16', '#418FDE']
      };
    } else {
      return {
        xData: xData1,
        yData1: yData2,
        yData2: yData4,
        colors: ['#6599FC', '#6953D8']
      };
    }
  }
  return {};
});
</script>

<style scoped>
.base-image {
  width: 100%;
  height: 100%;
  z-index: 1;
  position: relative;
}

.base-image::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(69, 222, 246, 0.8) 45deg,
    rgba(255, 255, 255, 0.6) 90deg,
    rgba(69, 222, 246, 0.8) 135deg,
    transparent 180deg,
    transparent 360deg
  );
  animation: orbit 3s linear infinite;
  z-index: -1;
}

.base-image::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: conic-gradient(
    from 180deg,
    transparent 0deg,
    rgba(255, 255, 255, 0.4) 45deg,
    rgba(69, 222, 246, 0.6) 90deg,
    rgba(255, 255, 255, 0.4) 135deg,
    transparent 180deg,
    transparent 360deg
  );
  animation: orbit 3s linear infinite reverse;
  z-index: -1;
}

@keyframes orbit {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.middle-section {
  display: flex;
  justify-content: space-between;
  padding: 1rem 2rem 0 2rem;
  height: calc(100vh - 9rem);
}

.left-section,
.center-section,
.right-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-section,
.right-section {
  height: 100%;
  width: 25%;
  box-sizing: border-box;
  overflow: hidden;
}

.chart-content {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  cursor: pointer;
}

.center-section {
  width: 48%;
  height: calc(100vh - 10.5rem);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  z-index: 1;
  margin-top: 1.5rem;
  animation: backgroundAnimation 2s ease-in-out;
}

.scroll-content-top {
  height: 65%;
  position: relative;
  background-color: rgba(14, 40, 73, 0.3);
  backdrop-filter: blur(0.625rem);
}

.scroll-content-bottom {
  height: 35%;
}

.base {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  z-index: 2;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  height: 90%;
  background-color: rgba(14, 40, 73, 0.35);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  color: #fff;
  margin: 0;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.modal-body {
  flex: 1;
  padding: 2rem;
  overflow: auto;
}

/* Transition animations */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

/* Rest of your existing styles... */
.cylinders {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.cylinder {
  position: absolute;
  width: 6.875rem;
  height: 6.875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.cylinder-image {
  width: 100%;
  height: auto;
}

.cylinder-number {
  color: #feffff;
  font-family: "Orbitron", "Rajdhani", "Audiowide", sans-serif;
  text-shadow: 0 0 10px #45def6, 0 0 20px rgba(255, 255, 255, 0.5);
  font-weight: bold;
  margin-bottom: 0;
  font-size: 2.8rem;
  font-family: "Arial", sans-serif;
}

.repair-duration {
  color: #fff;
  font-family: "Microsoft YaHei", sans-serif;
  font-weight: bold;
  font-size: 1rem;
  width: 100%;
  text-align: center;
  letter-spacing: 0.15rem;
}

.star-effect {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  pointer-events: none;
}

.chart-title {
  background: url("@/assets/image/V1-17.png") no-repeat center left;
  background-size: 80% 120%;
  background-position: left;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-left: -3rem;
  text-shadow: 0 0 10px #45def6, 0 0 20px rgba(255, 255, 255, 0.1);
}

.chart-container {
  background: none;
  background-color: rgba(14, 40, 73, 0.3);
  backdrop-filter: blur(0.625rem);
  transition: all 0.3s ease;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  animation: backgroundAnimation 2s ease-in-out;
}

@keyframes backgroundAnimation {
  0% {
    background-color: rgba(14, 40, 73, 0);
  }
  100% {
    background-color: rgba(14, 40, 73, 0.3);
  }
}

.marquee {
  overflow: hidden;
  position: relative;
  height: 100%;
}

.marquee-content {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  animation: marquee 20s linear infinite;
}

.table-header {
  width: 100%;
  height: 2rem;
  line-height: 2rem;
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0.8rem;
  background: rgba(39, 123, 185, 1);
  color: #cbeaf2;
  font-weight: bold;
  position: absolute;
  left: 0;
  right: 0;
  top: 0rem;
  z-index: 1;
}

.table-header span {
  width: 100%;
  text-align: left;
  letter-spacing: 0.1rem;
}

.table-content {
  overflow: hidden;
}

.table-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  color: #fff;
}

.table-item:nth-child(odd) span {
  width: 100%;
  font-size: 0.85rem;
  text-align: left;
  padding: 0.5rem 0.85rem;
  background-color: rgba(37, 118, 176, 0.1);
}

.table-item:nth-child(even) span {
  width: 100%;
  font-size: 0.85rem;
  text-align: left;
  padding: 0.5rem 0.8rem;
}

@keyframes marquee {
  0% {
    top: 100%;
  }
  100% {
    top: -100%;
  }
}

.marquee-content p {
  color: #fff;
  font-size: 1rem;
  text-align: center;
  margin: 0;
  padding: 0.5rem 0;
}

/* Fullscreen styles for the base component */
.fullscreen-base {
  width: 100%;
  height: 100%;
}

/* Fullscreen styles for the table */
.fullscreen-table {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
