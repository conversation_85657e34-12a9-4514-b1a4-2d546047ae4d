<template>
  <v-scale-screen
    ref="scale-screen"
    width="1920"
    height="1080"
    :fullScreen="true"
  >
    <div class="dashboard">
      <!-- Header -->
      <div class="header">
        <div class="header-left">
          <div
            class="title-item"
            :class="{ active: activeTab === 0 }"
            @click="switchTab(0)"
          >
            <h4>首页</h4>
          </div>
          <div
            class="title-item"
            :class="{ active: activeTab === 1 }"
            @click="switchTab(1)"
          >
            <h4>Model/机台信息</h4>
          </div>
        </div>
        <div class="header-center">
          <div class="title-wrapper">
            <h2>织造横机日常维修看板</h2>
            <div class="current-time">
              <span>{{ currentTime }} {{ weekDay }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <div
            class="title-item"
            :class="{ active: activeTab === 2 }"
            @click="switchTab(2)"
          >
            <h4>保全工信息</h4>
          </div>
          <div
            class="title-item"
            :class="{ active: activeTab === 3 }"
            @click="switchTab(3)"
          >
            <h4>挡车工信息</h4>
          </div>
        </div>
      </div>
      <!-- Middle Section -->
      <transition name="fade" mode="out-in">
        <component :is="currentMiddleComponent" :key="activeTab" />
      </transition>
    </div>
  </v-scale-screen>
</template>

<script>
import { ref, onMounted, defineComponent } from "vue";
import VScaleScreen from "v-scale-screen";
defineComponent({
  components: { VScaleScreen },
});
import MiddlePage1 from "@/views/MiddlePage1.vue";
import MiddlePage2 from "@/views/MiddlePage2.vue";
import MiddlePage3 from "@/views/MiddlePage3.vue";
import MiddlePage4 from "@/views/MiddlePage4.vue";

export default {
  components: { MiddlePage1, MiddlePage2, MiddlePage3, MiddlePage4 },
  data() {
    return {
      activeTab: 0,
      middleComponents: [MiddlePage1, MiddlePage2, MiddlePage3, MiddlePage4],
      lineData: [],
      lineLabels: [],
      barData: [],
      barCategories: [],
      pieData: [],
      tableData: [],
      currentTime: "",
      weekDay: "",
      timer: null,
    };
  },
  computed: {
    currentMiddleComponent() {
      return this.middleComponents[this.activeTab];
    },
  },
  methods: {
    switchTab(index) {
      this.activeTab = index;
    },
    autoSwitchTabs() {
      setInterval(() => {
        this.activeTab = (this.activeTab + 1) % this.middleComponents.length;
      }, 15000); // Switch every 5 seconds

    },    updateTime() {
      const now = new Date();
      const weekDays = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ];
      this.weekDay = weekDays[now.getDay()];
      this.currentTime = now
        .toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        })
        .replace(/\//g, "-");
    },
  },
  async mounted() {
    this.autoSwitchTabs();
    this.updateTime();
    this.timer = setInterval(this.updateTime, 1000);
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
};
</script>

<style>
.title-item {
  background: url("@/assets/image/V4-18.png") no-repeat center center;
  background-size: 100% 100%;
  background-position: center;
  cursor: pointer;
  font-size: 1.2rem !important;
  font-weight: bolder !important;
}

.title-item.active h4 {
  color: #f4ca16 !important;
  font-weight: bolder !important;
  font-size: 1.2rem !important;
  line-height: 0.4rem !important;
  text-shadow: 0 0 10px rgba(254, 255, 255, 0.5);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.title-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.current-time {
  color: #feffff;
  font-family: "Orbitron", "Rajdhani", sans-serif;
  font-weight: bolder;
  text-shadow: 0 0 10px rgba(58, 254, 254, 0.5);
  background: url("@/assets/image/V1-12.png") no-repeat center center;
  background-size: 110% 180%;
  background-position: center;
  padding: 0.35rem 6rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.current-time span {
  font-size: 1.2rem;
  font-weight: bolder;
  padding-top: 0.1rem;
  line-height: 2.6rem;
}
</style>
