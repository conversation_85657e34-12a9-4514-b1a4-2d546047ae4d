<template>
  <div class="middle-section">
    <!-- Top Row -->
    <div class="row">
      <div class="chart-content">
        <div class="chart-wrapper">
          <div class="chart-item line">
            <div class="chart-title">
              <h1>维修次数（按MODEL-机台）</h1>
            </div>
            <div class="chart-container">
              <ChartLine />
            </div>
          </div>
          <div class="chart-item ring">
            <div class="chart-title">
              <h1>维修次数（故障原因）</h1>
            </div>
            <div class="chart-container">
              <ChartPie />
            </div>
          </div>
        </div>
      </div>
      <div class="chart-content">
        <div class="chart-wrapper">
          <div class="chart-item line">
            <div class="chart-title">
              <h1>维修次数（日-周-月）</h1>
            </div>
            <div class="chart-container">
              <ChartLine />
            </div>
          </div>
          <div class="chart-item ring">
            <div class="chart-title">
              <h1>维修次数（维修人员）</h1>
            </div>
            <div class="chart-container">
              <ChartPie />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Row -->
    <div class="row">
      <div class="chart-content">
        <div class="chart-wrapper">
          <div class="chart-item line">
            <div class="chart-title">
              <h1>平均每次维修时间（MODEL-机台）</h1>
            </div>
            <div class="chart-container">
              <ChartLine />
            </div>
          </div>
          <div class="chart-item ring">
            <div class="chart-title">
              <h1>平均每次维修时间（故障原因）</h1>
            </div>
            <div class="chart-container">
              <ChartPie />
            </div>
          </div>
        </div>
      </div>
      <div class="chart-content">
        <div class="chart-wrapper">
          <div class="chart-item line">
            <div class="chart-title">
              <h1>平均每次维修时间（日-周-月）</h1>
            </div>
            <div class="chart-container">
              <ChartLine />
            </div>
          </div>
          <div class="chart-item ring">
            <div class="chart-title">
              <h1>平均每次维修时间（维修人员）</h1>
            </div>
            <div class="chart-container">
              <ChartPie />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ChartLine from "../components/ChartLine.vue";
import ChartPie from "../components/ChartPie.vue";

defineOptions({
  name: "MiddlePage2",
});

// Data for charts will go here
</script>

<style scoped>
.middle-section {
  display: flex;
  flex-direction: column;
  padding: 3rem 2rem 0 2rem;
  height: calc(100vh - 8rem);
}

.row {
  display: flex;
  flex-direction: row;
  gap: 0rem;
  flex: 1;
  height: 50%;
}

.chart-content {
  flex: 1;
  padding: 0.3rem;
}

.chart-wrapper {
  display: flex;
  gap: 0.5rem;
  height: 100%;
}

.chart-item {
  background-color: rgba(14, 40, 73, 0.3);
  backdrop-filter: blur(0.625rem);
  border-radius: 10px;
  gap: 0.5rem;
  animation: backgroundAnimation 2s ease-in-out;
}
@keyframes backgroundAnimation {
  0% {
    background-color: rgba(14, 40, 73, 0);
  }
  100% {
    background-color: rgba(14, 40, 73, 0.3);
  }
}

.chart-item.line {
  flex: 2;
}

.chart-item.ring {
  flex: 1;
}

.chart-container {
  background: none;
  height: calc(100% - 5rem);
  width: 100%;
}

h1 {
  color: #fff;
  text-align: center;
  margin-left: -4.5rem;
  text-shadow: 0 0 10px #45def6, 0 0 20px rgba(255, 255, 255, 0.1);
}
</style>
