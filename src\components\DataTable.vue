<template>
  <div ref="chart" class="chart-container"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'ChartLine',
  props: {
    data: Array,
    xLabels: Array,
  },
  mounted() {
    this.renderChart();
  },
  methods: {
    renderChart() {
      const chart = echarts.init(this.$refs.chart);
      const option = {
        xAxis: {
          type: 'category',
          data: this.xLabels,
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: this.data,
            type: 'line',
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
