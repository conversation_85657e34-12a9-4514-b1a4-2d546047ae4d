<script setup>
import Dashboard from './views/Dashboard.vue'
import { onMounted, onUnmounted } from 'vue'

const setFontSize = () => {
  // Get the viewport width
  const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0)
  // Base width is 1920px (design width)
  const baseWidth = 1920
  // Calculate scale ratio
  const ratio = vw / baseWidth
  // Set root font size
  document.documentElement.style.fontSize = `${16 * ratio}px`
}

onMounted(() => {
  setFontSize()
  window.addEventListener('resize', setFontSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', setFontSize)
})
</script>

<template>
  <div>
    <Dashboard />
  </div>
</template>

<style scoped>
:root {
  font-size: 16px;
}
</style>
