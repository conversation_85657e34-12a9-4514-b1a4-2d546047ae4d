<template>
  <div ref="chart" class="chart-pie"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "<PERSON><PERSON><PERSON>",
  props: {
    data: Array,
    xLabels: Array,
  },
  mounted() {
    this.renderChart();
    window.addEventListener("resize", this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    renderChart() {
      if (this.chart) {
        this.chart.dispose();
      }
      this.chart = echarts.init(this.$refs.chart);
      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c} ({d}%)"
        },
        legend: {
          show: true,
          orient: 'horizontal',
          top: '3%',
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        series: [
          {
            name: "数据分布",
            type: "pie",
            radius: ['30%', '50%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{d}',
              color: '#fff',
              fontSize: 14
            },
            labelLine: {
              show: true,
              length: 10,
              length2: 10,
              lineStyle: {
                color: '#fff'
              }
            },
            data: [
              { value: 1048, name: "织针" },
              { value: 735, name: "沉降片" },
              { value: 580, name: "挺针" },
            ],
          },
        ],
      };
      this.chart.setOption(option);
    },
  },
};
</script>

<style scoped>
.chart-pie {
  width: 100%;
  height: 100%;
}
</style>
