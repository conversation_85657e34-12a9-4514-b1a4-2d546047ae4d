{"name": "visualization-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@kjgl77/datav-vue3": "^1.7.4", "@vueuse/core": "^12.0.0", "axios": "^1.7.9", "echarts": "^5.5.1", "element-plus": "^2.9.1", "nprogress": "^0.2.0", "v-scale-screen": "^2.3.0", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sass-embedded": "^1.82.0", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.27.5", "vite": "^6.0.1"}}