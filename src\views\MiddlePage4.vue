<template>
  <div class="middle-section">
    <!-- Top Row -->
    <div class="chart-row">
      <div class="chart-wrapper" style="width: 20%; height: auto;">
        <div class="chart-title">
          <h3>班次员工数量</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <!-- Add content similar to the image layout here -->
            <div class="employee-stats">
              <div class="stat-item-wrapper">
                <img src="@/assets/image/4-1.png" alt="icon" class="stat-icon" />
                <div class="stat-item">
                  <span>A班员工数量</span>
                  <span>63</span>
                </div>
              </div>
              <div class="stat-item-wrapper">
                <img src="@/assets/image/4-1.png" alt="icon" class="stat-icon" />
                <div class="stat-item">
                  <span>B班员工数量</span>
                  <span>113</span>
                </div>
              </div>
              <div class="stat-item-wrapper">
                <img src="@/assets/image/4-1.png" alt="icon" class="stat-icon" />
                <div class="stat-item">
                  <span>C班员工数量</span>
                  <span>36</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-wrapper" style="width: 25%; height: auto;">
        <div class="chart-title">
          <h3>各班次织针库存</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <div class="shift-tabs">
              <div
                v-for="(shift, index) in shifts"
                :key="index"
                :class="{ active: currentShift === index }"
                @click="currentShift = index"
                class="shift-tab"
              >
                <span>{{ shift.name }}</span>
              </div>
            </div>
            <transition name="slide-fade" mode="out-in">
              <div class="shift-stats" :key="currentShift">
                <div class="stat-item-wrappers" v-for="(item, index) in shifts[currentShift].items" :key="index">
                  <span>{{ item.name }}</span>
                  <span>{{ item.value }}</span>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
      <div class="chart-wrapper" style="width: 25%; height: auto;">
        <div class="chart-title">
          <h3>维修次数（班次-员工）</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <ChartBar
              :xData="repairByShiftData.xData"
              :yData1="repairByShiftData.yData1"
              :yData2="repairByShiftData.yData2"
              :colors="['#F4CA16', '#418FDE']"
              :legendData="['维修次数', '平均维修时间']"
            />
          </div>
        </div>
      </div>
      <div class="chart-wrapper" style="width: 30%; height: auto;">
        <div class="chart-title">
          <h3>维修成功数（MODEL）</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <ChartBar
              :xData="repairByModelData.xData"
              :yData1="repairByModelData.yData1"
              :yData2="repairByModelData.yData2"
              :colors="['#45DEF6', '#F4CA16']"
              :legendData="['维修成功数', '平均维修时间']"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Row -->
    <div class="chart-row">
      <div class="chart-wrapper" style="width: 40%; height: auto;">
        <div class="chart-title">
          <h3>维修次数(日-周-月)</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <ChartLine />
          </div>
        </div>
      </div>
      <div class="chart-wrapper" style="width: 25%; height: auto;">
        <div class="chart-title">
          <h3>各班次针织使用数量对比</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <!-- Add table content here -->
            <ChartPie />
          </div>
        </div>
      </div>
      <div class="chart-wrapper" style="width: 35%; height: auto;">
        <div class="chart-title">
          <h3>维修次数（故障原因）</h3>
        </div>
        <div class="chart-container">
          <div class="chart-content">
            <ChartPie />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import ChartPie from "@/components/ChartPie.vue";
import ChartBar from "@/components/ChartBar.vue";
import ChartLine from "@/components/ChartLine.vue";

const shifts = ref([
  { name: "A班", items: [{ name: "织针", value: 230 }, { name: "沉降片", value: 150 }, { name: "挺针", value: 50 }, { name: "齿口片", value: 100 }, { name: "中间片", value: 250 },{ name: "加厚中间片", value: 100 }, { name: "加厚挺针", value: 250 }] },
  { name: "B班", items: [{ name: "织针", value: 200 }, { name: "沉降片", value: 130 }, { name: "挺针", value: 60 }, { name: "齿口片", value: 90 }, { name: "中间片", value: 240 },{ name: "加厚中间片", value: 100 }, { name: "加厚挺针", value: 250 }] },
  { name: "C班", items: [{ name: "织针", value: 210 }, { name: "沉降片", value: 140 }, { name: "挺针", value: 55 }, { name: "齿口片", value: 95 }, { name: "中间片", value: 245 },{ name: "加厚中间片", value: 100 }, { name: "加厚挺针", value: 250 }] },
]);

const currentShift = ref(0);

// 维修次数（班次-员工）数据
const repairByShiftData = ref({
  xData: ['A班-张三', 'A班-李四', 'B班-王五', 'B班-赵六', 'C班-孙七', 'C班-周八'],
  yData1: [45, 38, 52, 41, 35, 48], // 维修次数
  yData2: [12, 15, 10, 18, 14, 11]  // 平均每次维修时间（分钟）
});

// 维修成功数（MODEL）数据
const repairByModelData = ref({
  xData: ['Model-A1', 'Model-B2', 'Model-C3', 'Model-D4', 'Model-E5'],
  yData1: [85, 92, 78, 88, 95], // 维修成功数
  yData2: [8, 6, 12, 9, 5]      // 平均维修时间（分钟）
});

onMounted(() => {
  setInterval(() => {
    currentShift.value = (currentShift.value + 1) % shifts.value.length;
  }, 15000); // Switch every 5 seconds
});

defineOptions({
  name: "MiddlePage4",
});
</script>

<style scoped>
.middle-section {
  margin-top: 3rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  gap: 1rem;
  padding: 1rem 2rem;
}

.chart-row {
  display: flex;
  flex: 1;
  gap: 1rem;
  min-height: 0;
}

.chart-wrapper {
  display: flex;
  flex-direction: column;
  height: auto;
}

.chart-container {
  background: none;
  border-radius: 0.625rem;
  padding: 1rem;
  flex: 1;
  background-color: rgba(14, 40, 73, 0.3);
  backdrop-filter: blur(0.625rem);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 0;
  animation: backgroundAnimation 2s ease-in-out;
}

@keyframes backgroundAnimation {
  0% {
    background-color: rgba(14, 40, 73, 0);
  }
  100% {
    background-color: rgba(14, 40, 73, 0.3);
  }
}

.chart-title {
  width: 100%;
  height: 4.5rem;
  margin-bottom: 1rem;
  background: url("@/assets/image/V1-17.png") no-repeat left center;
  background-size: 80% 120%;
  background-position: left;
  display: flex;
  align-items: center;
}

.chart-content {
  flex: 1;
  min-height: 0;
  width: 100%;
}

h3 {
  margin: 0;
  color: #fff;
  text-align: left;
  font-size: 1.2em;
  font-style: italic;
  font-weight: bolder;
  margin-left: 5.9rem;
  text-shadow: 0 0 10px #45def6, 0 0 20px rgba(255, 255, 255, 0.1);
}

.employee-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.shift-stats {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}
.stat-item-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.stat-item-wrappers {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(100% / 3.65);
  background: url("@/assets/image/popUP_bg.png") no-repeat center center;
  background-size: 100% 100%;
  background-position: center;
  padding: 0.5rem;
}
.stat-item-wrappers span {
  padding: 0rem 0.5rem;
  color: #fff;
  flex: 1;
}
.stat-item-wrappers span:nth-child(2) {
  font-size: 1.6rem;
  font-weight: bolder;
  margin: 0.3rem 1rem;
  text-align: left;
  color: #45def6;
}

.stat-icon {
  width: 6rem;
  height: 5.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  color: #fff;
  background-color: rgba(37, 118, 176, 0.1);
  padding: 0.5rem;
  border-radius: 0.5rem;
  flex: 2;
}
.stat-item span {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 0.3rem 1rem;
  text-align: left;
}
.stat-item span:nth-child(2) {
  font-size: 1.6rem;
  font-weight: bolder;
  margin: 0.3rem 1rem;
  text-align: left;
  color: #F4CA16;
}

.shift-tabs {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
}

.shift-tab {
  cursor: pointer;
  padding: 0.5rem 1rem;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 500;
}

.shift-tab.active {
  border-bottom: 2px solid #45def6;
}

.slide-fade-enter-active, .slide-fade-leave-active {
  transition: all 0.5s ease;
}

.slide-fade-enter-from, .slide-fade-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.knit-usage-table {
  width: 100%;
  border-collapse: collapse;
  color: #fff;
}

.knit-usage-table th, .knit-usage-table td {
  border: 1px solid #fff;
  padding: 0.5rem;
  text-align: left;
}

.knit-usage-table th {
  background-color: rgba(37, 118, 176, 0.3);
}

.knit-usage-table td {
  background-color: rgba(37, 118, 176, 0.1);
}
</style>
