<template>
  <div style="width: 100%; height: 100%">
    <div style="width: 100%; height: 100%">
      <dv-capsule-chart :config="config" style="width: 100%; height: 85%" />
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";

const config = reactive({
  data: [
    {
      name: "INVINCIBLE 3",
      value: 437,
    },
    {
      name: "PEGASUS TURBO 4",
      value: 419,
    },
    {
      name: "INFINITY RUN 4",
      value: 335,
    },
    {
      name: "FREE 25",
      value: 321,
    },
    {
      name: "VAPORMAX 2023",
      value: 270,
    },
    {
      name: "INTERACT",
      value: 264,
    },
  ],
  colors: ["#32c5e9", "#96bfff"],
  unit: "次",
  labelNum: 8,
  showValue: true,
});
</script>
