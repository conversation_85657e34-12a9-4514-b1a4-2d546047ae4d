<template>
  <div ref="chart" class="chart-box"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  data: Array,
  xLabels: Array,
});

const chart = ref(null);
let chartInstance = null;

const renderChart = () => {
  // 确保DOM元素存在
  if (!chart.value) {
    console.warn('Chart container not ready');
    return;
  }

  // 如果已存在图表实例，先销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  try {
    chartInstance = echarts.init(chart.value);
    const option = {
      title: {
        text: "",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      color: ["#0098F1", "#03C799"],
      legend: {
        data: ["等待时间"],
        left: "center",
        top: "1%",
        textStyle: {
          color: "#fff",
        },
      },
      grid: {
        top: "10%",
        left: "3%",
        right: "5%",
        bottom: "5%",
        containLabel: true,
      },
      xAxis: {
        type: "value",
        boundaryGap: [0, 0.01],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff'
        }
      },
      yAxis: {
        type: "category", 
        data: ["张忠情", "黄少峰", "刘爱芳", "王志春", "孙春光", "陈文强"],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff'
        }
      },
      series: [
        {
          name: "等待时间",
          type: "bar",
          barWidth: '45%',
          barGap: '5%',
          barCategoryGap: '10%',
          data: [22.6, 18.4, 14.5, 15.6, 21.7, 8.5],
          label: {
            show: true,
            position: 'insideRight',
            color: '#fff',
            formatter: '{c}分钟'
          },
          itemStyle: {
            borderRadius: [0, 0, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#0098F1' },
              { offset: 1, color: '#03C799' }
            ])
          }
        },
      ],
    };
    
    chartInstance.setOption(option);
  } catch (error) {
    console.error('Chart initialization failed:', error);
  }
};

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  // 使用nextTick确保DOM完全渲染
  nextTick(() => {
    renderChart();
  });
  
  // 监听window大小变化
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener("resize", handleResize);
  
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped lang="scss">
.chart-box {
  width: 100%;
  height: 100%;
  min-height: 200px; // 确保容器有最小高度
}
</style>
