<template>
  <div ref="chart" class="chart-box"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  data: Array,
  indicators: Array,
});

const chart = ref(null);

const renderChart = () => {
  const chartInstance = echarts.init(chart.value);
  const option = {
    color: ["#67F9D8", "#FFE434", "#56A3F1", "#FF917C"],
    title: {
      text: "",
    },
    legend: {
      show: true,
      textStyle: {
        color: "#fff",
      },
    },
    radar: [
      {
        indicator: [
          { text: "Indicator1" },
          { text: "Indicator2" },
          { text: "Indicator3" },
          { text: "Indicator4" },
          { text: "Indicator5" },
        ],
        center: ["50%", "50%"],
        radius: 80,
        startAngle: 90,
        splitNumber: 4,
        shape: "",
        axisName: {
          formatter: "【{value}】",
          color: "#428BD4",
        },
        splitArea: {
          areaStyle: {
            color: ["#77EADF", "#26C3BE", "#64AFE9", "#428BD4"],
            shadowColor: "rgba(0, 0, 0, 0.2)",
            shadowBlur: 10,
          },
        },
        axisLine: {
          lineStyle: {
            color: "rgba(211, 253, 250, 0.8)",
          },
        },
        splitLine: {
          lineStyle: {
            color: "rgba(211, 253, 250, 0.8)",
          },
        },
      },
    ],
    series: [
      {
        type: "radar",
        emphasis: {
          lineStyle: {
            width: 4,
          },
        },
        data: [
          {
            value: [100, 8, 0.4, -80, 2000],
            name: "Data A",
          },
          {
            value: [60, 5, 0.3, -100, 1500],
            name: "Data B",
            areaStyle: {
              color: "rgba(255, 228, 52, 0.6)",
            },
          },
        ],
      },
    ],
  }
  chartInstance.setOption(option);
};

onMounted(() => {
  renderChart();
});

watch(
  () => props.data,
  () => {
    renderChart();
  }
);
</script>

<style scoped>
.chart-box {
  width: 100%;
  height: 100%;
}
</style>
