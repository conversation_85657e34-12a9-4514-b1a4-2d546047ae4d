<template>
  <div ref="chart" class="chart-contents"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  xData: {
    type: Array,
    default: () => ['A班-张三', 'A班-李四', 'B班-王五', 'B班-赵六', 'C班-孙七']
  },
  yData1: {
    type: Array,
    default: () => [45, 38, 52, 41, 35]
  },
  yData2: {
    type: Array,
    default: () => [12, 15, 10, 18, 14]
  },
  colors: {
    type: Array,
    default: () => ['#F4CA16', '#418FDE']
  },
  legendData: {
    type: Array,
    default: () => ['维修次数', '平均每次维修时间']
  }
});

const chart = ref(null);

const renderChart = () => {
  if (!chart.value) return;

  chartInstance = echarts.init(chart.value);
  const option = {
    tooltip: {
      trigger: "axis", 
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    color: props.colors || ['#F4CA16', '#418FDE'], // Use props for colors
    toolbox: {
      show: false,
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ["line", "bar"] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: props.legendData,
      left: 'center',
      top: '5%',
      textStyle: {
        color: '#fff',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: props.xData, // Use props for x-axis data
        axisPointer: {
          type: "shadow",
        },
        axisLabel: {
          show: true,
          color: '#fff',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false
        },
        splitArea: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "",
        min: 0,
        max: 250,
        interval: 50,
        axisLabel: {
          show: true,
          formatter: "{value} 次",
          color: '#fff',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false
        },
        splitArea: {
          show: false,
        },
      },
      {
        type: "value",
        name: "",
        min: 0,
        max: 25,
        interval: 5,
        axisLabel: {
          show: true,
          formatter: "{value} 分钟",
          color: '#fff',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false
        },
        splitArea: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: props.legendData[0],
        type: "bar",
        barWidth: '45%',
        tooltip: {
          valueFormatter: function (value) {
            return value + (props.legendData[0].includes('成功') ? '次' : '次');
          },
        },
        data: props.yData1, // Use props for series data
        label: {
          show: true,
          position: 'top',
          color: '#fff',
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: props.colors ? props.colors[0] : '#F4CA16' },
            { offset: 1, color: props.colors ? props.colors[1] : '#418FDE' }
          ])
        }
      },
      {
        name: props.legendData[1],
        type: "line",
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + "分钟";
          },
        },
        data: props.yData2, // Use props for series data
        label: {
          show: true,
          position: 'top',
          color: '#fff',
        },
      },
    ],
  };
  chartInstance.setOption(option);
};

let chartInstance = null;

onMounted(() => {
  renderChart();

  // 添加窗口大小变化监听
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  });
});

watch(() => [props.xData, props.yData1, props.yData2, props.colors, props.legendData], () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  renderChart();
}, { deep: true });
</script>

<style>
.chart-contents {
  width: 100%;
  height: 100%;
}
</style>
