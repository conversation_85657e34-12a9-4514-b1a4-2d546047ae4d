<template>
  <div ref="chart" class="chart-contents"></div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  xData: Array,
  yData1: Array,
  yData2: Array,
  colors: Array, // Add colors as a prop
});

const chart = ref(null);

const renderChart = () => {
  const chartInstance = echarts.init(chart.value);
  const option = {
    tooltip: {
      trigger: "axis", 
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    color: props.colors || ['#F4CA16', '#418FDE'], // Use props for colors
    toolbox: {
      show: false,
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ["line", "bar"] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: ["维修次数", "平均每次维修时间"],
      left: 'center',
      top: '5%',
      textStyle: {
        color: '#fff',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: props.xData, // Use props for x-axis data
        axisPointer: {
          type: "shadow",
        },
        axisLabel: {
          show: true,
          color: '#fff',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false
        },
        splitArea: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "",
        min: 0,
        max: 250,
        interval: 50,
        axisLabel: {
          show: true,
          formatter: "{value} 次",
          color: '#fff',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false
        },
        splitArea: {
          show: false,
        },
      },
      {
        type: "value",
        name: "",
        min: 0,
        max: 25,
        interval: 5,
        axisLabel: {
          show: true,
          formatter: "{value} 分钟",
          color: '#fff',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false
        },
        splitArea: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: "维修次数",
        type: "bar",
        barWidth: '45%',
        tooltip: {
          valueFormatter: function (value) {
            return value + "次";
          },
        },
        data: props.yData1, // Use props for series data
        label: {
          show: true,
          position: 'top',
          color: '#fff',
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: props.colors ? props.colors[0] : '#F4CA16' },
            { offset: 1, color: props.colors ? props.colors[1] : '#418FDE' }
          ])
        }
      },
      {
        name: "平均每次维修时间",
        type: "line",
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + "分钟";
          },
        },
        data: props.yData2, // Use props for series data
        label: {
          show: true,
          position: 'top',
          color: '#fff',
        },
      },
    ],
  };
  chartInstance.setOption(option);
};

onMounted(() => {
  renderChart();
});

watch(() => [props.xData, props.yData1, props.yData2, props.colors], renderChart);
</script>

<style>
.chart-contents {
  width: 100%;
  height: 100%;
}
</style>
