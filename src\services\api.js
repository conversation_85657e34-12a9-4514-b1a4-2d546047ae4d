import axios from "axios";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { ElMessage } from "element-plus";

const api = axios.create({
  baseURL: "http://localhost:8080",
  timeout: 5000,
});

api.interceptors.request.use(
  (config) => {
    NProgress.start();
    // 在请求头中添加通用配置
    return config;
  },
  (error) => {
    NProgress.done();
    ElMessage.error("请求错误");
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    NProgress.done();
    return response.data;
  },
  (error) => {
    NProgress.done();
    if (error.response) {
      switch (error.response.status) {
        case 400:
          ElMessage.error("请求错误");
          break;
        case 401:
          ElMessage.error("未授权，请登录");
          break;
        case 403:
          ElMessage.error("拒绝访问");
          break;
        case 404:
          ElMessage.error("请求地址出错");
          break;
        case 500:
          ElMessage.error("服务器内部错误");
          break;
        default:
          ElMessage.error("未知错误");
      }
    } else {
      ElMessage.error("网络错误");
    }
    return Promise.reject(error);
  }
);

export default api;
