<template>
  <div class="middle-section">
    <div class="content-grid">
      <div class="chart-block">
        <div class="chart-title">
          <h1>各机修修车次数分析</h1>
        </div>
        <div class="charts-container custom-layout">
          <div class="large cover-fit">
            <div class="chart-header">
              <h3>
                <img
                  src="@/assets/image/1.png"
                  class="title-icon"
                />维修次数(班次-保全)
              </h3>
            </div>
            <div class="chart-content">
              <ChartLine />
            </div>
          </div>
          <div class="small-charts">
            <div class="chart-containers">
              <h3>
                <img
                  src="@/assets/image/1.png"
                  class="title-icon"
                />维修次数(按MODEL)
              </h3>
              <!-- Chart 2 -->
              <ChartPie />
            </div>
            <div class="chart-containers">
              <h3>
                <img
                  src="@/assets/image/1.png"
                  class="title-icon"
                />维修次数(按故障原因)
              </h3>
              <!-- Chart 3 -->
              <ChartPie />
            </div>
          </div>
        </div>
      </div>

      <div class="chart-block">
        <div class="chart-title">
          <h1>各机修修车成功率分析</h1>
        </div>
        <div class="charts-container custom-layout">
          <div class="small-charts">
            <div class="chart-containers">
              <h3>
                <img
                  src="@/assets/image/1.png"
                  class="title-icon"
                />维修成功数（MODEL）
              </h3>
              <!-- Chart 1 -->
              <ChartPie />
            </div>
            <div class="chart-containers">
              <h3>
                <img
                  src="@/assets/image/1.png"
                  class="title-icon"
                />维修成功数（故障原因）
              </h3>
              <!-- Chart 2 -->
              <ChartPie />
            </div>
          </div>
          <div class="large cover-fit">
            <div class="chart-header">
              <h3>
                <img
                  src="@/assets/image/1.png"
                  class="title-icon"
                />维修成功率、维修成功数和维修次数
              </h3>
            </div>
            <div class="chart-content">
              <ChartLine />
            </div>
          </div>
        </div>
      </div>

      <div class="chart-block">
        <div class="chart-title">
          <h1>各机修平均每次接单等待时间分析</h1>
        </div>
        <div class="charts-container vertical-layout">
          <div class="top-section">
            <div class="chart-containers main-chart">
              <h3>
                <img src="@/assets/image/1.png" class="title-icon" />平均每次接单等待时间（班次）
              </h3>
              <ChartLine />
            </div>
            <div class="side-charts">
              <div class="chart-containers">
                <h3>
                  <img src="@/assets/image/1.png" class="title-icon" />平均每次接单等待时间（MODEL）
                </h3>
                <div class="chart-wrapper">
                  <ChartPie />
                </div>
              </div>
              <div class="chart-containers">
                <h3>
                  <img src="@/assets/image/1.png" class="title-icon" />平均每次接单等待时间（故障原因）
                </h3>
                <div class="chart-wrapper">
                  <ChartPie />
                </div>
              </div>
            </div>
          </div>
          <div class="bottom-section">
            <div class="chart-containers full-width">
              <h3>
                <img src="@/assets/image/1.png" class="title-icon" />维修等待时间(日-周-月)
              </h3>
              <ChartLine />
            </div>
          </div>
        </div>
      </div>

      <div class="chart-block">
        <div class="chart-title">
          <h1>各机修平均每次维修时间分析</h1>
        </div>
        <div class="charts-container vertical-layout">
          <div class="top-section">
            <div class="chart-containers main-chart">
              <h3>
                <img src="@/assets/image/1.png" class="title-icon" />平均每次维修时间（班次）
              </h3>
              <ChartLine />
            </div>
            <div class="side-charts">
              <div class="chart-containers">
                <h3>
                  <img src="@/assets/image/1.png" class="title-icon" />平均每次维修时间（MODEL）
                </h3>
                <div class="chart-wrapper">
                  <ChartPie />
                </div>
              </div>
              <div class="chart-containers">
                <h3>
                  <img src="@/assets/image/1.png" class="title-icon" />平均每次维修时间（故障原因）
                </h3>
                <div class="chart-wrapper">
                  <ChartPie />
                </div>
              </div>
            </div>
          </div>
          <div class="bottom-section">
            <div class="chart-containers full-width">
              <h3>
                <img src="@/assets/image/1.png" class="title-icon" />平均每次维修时间(日-周-月)
              </h3>
              <ChartLine />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ChartLine from "@/components/ChartLine.vue";
import ChartPie from "@/components/ChartPie.vue";
defineOptions({
  name: "MiddlePage3",
});
</script>

<style scoped lang="scss">
.cover-fit {
  background-color: rgba(14, 40, 73, 0.3);
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 0.625rem;
  padding: 0rem 0rem 0.625rem 0rem;
  animation: backgroundAnimation 2s ease-in-out;
}

.chart-content {
  flex: 1;
  min-height: 0;
}

.chart-header {
  padding: 0.3rem 0.625rem;
}

.middle-section {
  height: calc(100vh - 5.5rem);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  padding: 1rem 2rem;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 0.5rem;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.chart-block {
  margin-top: 1rem;
  padding: 0.25rem 0.2rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.chart-title {
  margin-bottom: 1.25rem;
  padding: 0 0.625rem;
  border-bottom: 0.125rem solid rgba(74, 144, 226, 0.3);
  background: url("@/assets/image/V1-17.png") no-repeat center left;
  background-size: 50% 150%;
  background-position: left;
}

.chart-title h1 {
  color: #fff;
  font-size: 1.2em;
  margin: 0;
  font-weight: bolder;
  margin-left: -3rem;
  text-shadow: 0 0 10px #45def6, 0 0 20px rgba(255, 255, 255, 0.1);
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  height: 100%;
  padding: 0.625rem;
  box-sizing: border-box;
  overflow: hidden;
}

.charts-container.custom-layout {
  display: flex;
  gap: 0.9375rem;
}

.charts-container.vertical-layout {
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
}

.top-section {
  display: flex;
  gap: 0.9375rem;
  height: 100%;
}

.bottom-section {
  height: 100%;
}

.main-chart {
  flex: 1;
}

.side-charts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
}

.side-charts .chart-containers {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-wrapper {
  flex: 1;
  min-height: 0;
  height: 100%;
}

.full-width {
  width: 100%;
  height: 100%;
}

.large {
  flex: 2;
  height: 100%;
  min-height: 0;
}

.small-charts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
  min-height: 0;
}

.small-charts .chart-containers {
  flex: 1;
  height: calc(50% - 0.468rem);
  margin: 0;
  min-height: 0;
}

.chart-containers {
  backdrop-filter: blur(0.625rem);
  transition: all 0.3s ease;
  background-color: rgba(14, 40, 73, 0.3);
  display: flex;
  flex-direction: column;
  min-height: 0;
  border-radius: 0.625rem;
  animation: backgroundAnimation 2s ease-in-out;
}

@keyframes backgroundAnimation {
  0% {
    background-color: rgba(14, 40, 73, 0);
  }
  100% {
    background-color: rgba(14, 40, 73, 0.3);
  }
}

.chart-containers:hover {
  transform: translateY(-0.1875rem);
  box-shadow: 0 0.1875rem 0.5625rem rgba(255, 255, 255, 0.2);
}

h3 {
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  letter-spacing: 0.01em;
  padding-left: 1rem;
}

.title-icon {
  width: 1.2em;
  height: 1.2em;
  margin-right: 0.3em;
}
</style>
